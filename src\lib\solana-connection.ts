import { Connection } from "@solana/web3.js";
import { solanaNetworks, SolanaNetwork } from "./solana-config";

// Connection pool for different networks
const connectionPool = new Map<string, Connection>();

// Create a connection with fallback support
export function createSolanaConnection(
  network: SolanaNetwork = "mainnet"
): Connection {
  const networkConfig = solanaNetworks[network];
  const cacheKey = `${network}-${networkConfig.endpoint}`;

  // Return cached connection if available
  if (connectionPool.has(cacheKey)) {
    return connectionPool.get(cacheKey)!;
  }

  // Create new connection
  const connection = new Connection(networkConfig.endpoint, {
    commitment: "confirmed",
    confirmTransactionInitialTimeout: 60000, // 60 seconds
    disableRetryOnRateLimit: true, // Disable automatic retries to prevent rate limiting
  });

  // Cache the connection
  connectionPool.set(cacheKey, connection);

  return connection;
}

// Get connection with automatic fallback (optimized to reduce API calls)
export async function getConnectionWithFallback(
  network: SolanaNetwork = "mainnet"
): Promise<Connection> {
  // First try to use cached connection
  const cachedConnection = createSolanaConnection(network);
  if (cachedConnection) {
    return cachedConnection;
  }

  const networkConfig = solanaNetworks[network];
  const endpoints = networkConfig.fallbackEndpoints || [networkConfig.endpoint];

  // Try endpoints without testing them first to reduce API calls
  for (let i = 0; i < endpoints.length; i++) {
    try {
      const endpoint = endpoints[i];
      const cacheKey = `${network}-${endpoint}`;

      // Check if we already have this connection cached
      if (connectionPool.has(cacheKey)) {
        return connectionPool.get(cacheKey)!;
      }

      const connection = new Connection(endpoint, {
        commitment: "confirmed",
        confirmTransactionInitialTimeout: 30000,
        disableRetryOnRateLimit: true, // Disable automatic retries to prevent rate limiting
      });

      // Cache the connection without testing it first
      connectionPool.set(cacheKey, connection);

      return connection;
    } catch (error) {
      // If this is the last endpoint, throw the error
      if (i === endpoints.length - 1) {
        throw new Error(`Failed to connect to any Solana ${network} endpoint`);
      }
    }
  }

  // This should never be reached, but TypeScript requires it
  throw new Error(`No valid endpoints found for ${network}`);
}

// Retry function with exponential backoff (optimized to reduce rate limiting)
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 2, // Reduced from 3 to 2
  baseDelay: number = 2000 // Increased from 1000 to 2000ms
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;

      // Don't retry on certain errors
      if (
        error.message?.includes("Invalid") ||
        error.message?.includes("Not found") ||
        error.message?.includes("429") || // Don't retry on rate limit
        error.message?.includes("Too Many Requests")
      ) {
        throw error;
      }

      if (attempt === maxRetries) {
        break;
      }

      // Calculate delay with exponential backoff (longer delays)
      const delay = baseDelay * Math.pow(2, attempt);

      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

// Enhanced balance fetching with fallback and retry
export async function fetchBalanceWithFallback(
  publicKey: any,
  network: SolanaNetwork = "mainnet"
): Promise<number> {
  return retryWithBackoff(async () => {
    const connection = await getConnectionWithFallback(network);
    return await connection.getBalance(publicKey);
  });
}
